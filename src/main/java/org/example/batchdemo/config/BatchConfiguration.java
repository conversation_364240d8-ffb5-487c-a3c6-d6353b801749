package org.example.batchdemo.config;

import org.example.batchdemo.batch.EigenFileReader;
import org.example.batchdemo.batch.PaymentProcessor;
import org.example.batchdemo.batch.ValidationProcessor;
import org.example.batchdemo.batch.processor.EigenToSapProcessor;
import org.example.batchdemo.batch.reader.EigenBatchDetailReader;
import org.example.batchdemo.batch.writer.ClassifiedSapWriter;
import org.example.batchdemo.model.eigen.EigenBatchDetail;
import org.example.batchdemo.model.eigen.EigenRootGroup;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.support.CompositeItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.Arrays;

@Configuration
public class BatchConfiguration {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private EigenFileReader eigenFileReader;

    @Autowired
    private EigenFixedLengthReader eigenFixedLengthReader;

    @Autowired
    private ValidationProcessor validationProcessor;

    @Autowired
    private PaymentProcessor paymentProcessor;

    @Autowired
    private EigenToSapProcessor eigenToSapProcessor;

    @Autowired
    private ClassifiedSapWriter classifiedSapWriter;

    @Bean
    public Job opacFileProcessingJob() {
        return new JobBuilder("opacFileProcessingJob", jobRepository)
                .start(processFileStep())
                .next(convertToSapStep())
                .build();
    }

    @Bean
    public Step processFileStep() {
        return new StepBuilder("processFileStep", jobRepository)
                .<EigenRootGroup, EigenRootGroup>chunk(1, transactionManager)
                .reader(eigenFileReader)
                .processor(compositeProcessor())
                .writer(fileWriter())
                .build();
    }

    @Bean
    public ItemProcessor<EigenRootGroup, EigenRootGroup> compositeProcessor() {
        CompositeItemProcessor<EigenRootGroup, EigenRootGroup> processor = new CompositeItemProcessor<>();
        processor.setDelegates(Arrays.asList(validationProcessor, paymentProcessor));
        return processor;
    }

    @Bean
    public Step convertToSapStep() {
        return new StepBuilder("convertToSapStep", jobRepository)
                .<EigenBatchDetail, Object>chunk(10, transactionManager)
                .reader(eigenBatchDetailReader)
                .processor(eigenToSapProcessor)
                .writer(classifiedSapWriter)
                .build();
    }

    @Bean
    public ItemWriter<EigenRootGroup> fileWriter() {
        return items -> {
            for (EigenRootGroup item : items) {
                // This is where we would write the processed files
                // For now, just log the completion
                System.out.println("Processed file with " +
                    item.getFileTrailer().getTotalDetailLinesInFile() + " detail lines");
            }
        };
    }
}
