package org.example.batchdemo.batch.writer;

import org.example.batchdemo.model.sap.declined.BFKKRK;
import org.example.batchdemo.model.sap.declined.BFKKRP;
import org.example.batchdemo.model.sap.declined.SAPDeclinedRootGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Native Spring Batch ItemWriter for SAP Declined fixed-length format files
 * Writes files without BeanIO dependency
 */
@Component
public class SapDeclinedFixedLengthWriter implements ItemWriter<SAPDeclinedRootGroup> {

    private static final Logger logger = LoggerFactory.getLogger(SapDeclinedFixedLengthWriter.class);

    @Value("${batch.output.directory:#{systemProperties['java.io.tmpdir']}/batch/output}")
    private String outputDirectory;

    @Value("${sap.client:100}")
    private String sapClient;

    @Override
    public void write(Chunk<? extends SAPDeclinedRootGroup> chunk) throws Exception {
        logger.info("Writing {} SAP declined records", chunk.size());

        // Ensure output directory exists
        Path outputPath = Paths.get(outputDirectory);
        if (!Files.exists(outputPath)) {
            Files.createDirectories(outputPath);
            logger.info("Created output directory: {}", outputPath);
        }

        for (SAPDeclinedRootGroup sapDeclinedRootGroup : chunk) {
            writeSapDeclinedFile(sapDeclinedRootGroup);
        }
    }

    private void writeSapDeclinedFile(SAPDeclinedRootGroup sapDeclinedRootGroup) throws IOException {
        // Generate filename with timestamp
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date());
        String fileName = String.format("sap_declined_%s.txt", timestamp);
        Path outputFile = Paths.get(outputDirectory, fileName);

        logger.info("Writing SAP declined file: {}", fileName);

        try (BufferedWriter writer = Files.newBufferedWriter(outputFile, 
                StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING)) {
            
            // Write header record (BFKKZGR00)
            if (sapDeclinedRootGroup.getBfkkzgr00() != null) {
                writeHeaderRecord(writer, sapDeclinedRootGroup.getBfkkzgr00());
            }

            // Write declined payment header (BFKKRK)
            if (sapDeclinedRootGroup.getBfkkrk() != null) {
                writeDeclinedHeaderRecord(writer, sapDeclinedRootGroup.getBfkkrk());
            }

            // Write declined payment details (BFKKRP)
            if (sapDeclinedRootGroup.getBfkkrpList() != null) {
                for (BFKKRP detail : sapDeclinedRootGroup.getBfkkrpList()) {
                    writeDeclinedDetailRecord(writer, detail);
                }
            }
            
            logger.info("Successfully wrote SAP declined file: {} with {} declined records", 
                fileName, 
                sapDeclinedRootGroup.getBfkkrpList() != null ? sapDeclinedRootGroup.getBfkkrpList().size() : 0);
        }
    }

    private void writeHeaderRecord(BufferedWriter writer, org.example.batchdemo.model.sap.declined.BFKKZGR00 header) throws IOException {
        StringBuilder line = new StringBuilder();
        
        // STYPE (1) + MANDT (3) + APPLK (1) = 5 characters
        line.append(formatString(header.getStype(), 1));
        line.append(formatString(header.getMandt() != null ? header.getMandt() : sapClient, 3));
        line.append(formatString(header.getApplk(), 1));
        
        writer.write(line.toString());
        writer.newLine();
    }

    private void writeDeclinedHeaderRecord(BufferedWriter writer, BFKKRK header) throws IOException {
        StringBuilder line = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        
        // Build fixed-length record (257 characters total)
        line.append(formatString(header.getStype(), 1));                    // 1
        line.append(formatString(header.getTbnam(), 30));                   // 30
        line.append(formatString(header.getKeyr1(), 12));                   // 12
        line.append(formatString(header.getKeyr2(), 40));                   // 40
        line.append(formatString(header.getFikey(), 12));                   // 12
        line.append(formatString(header.getRlsko(), 10));                   // 10
        line.append(formatString(header.getBukrs(), 4));                    // 4
        line.append(formatString(header.getGsber(), 4));                    // 4
        line.append(formatString(header.getBlart(), 2));                    // 2
        line.append(formatString(header.getWaers(), 5));                    // 5
        line.append(formatBigDecimal(header.getKursf(), 10));               // 10
        line.append(formatDate(header.getBudat(), dateFormat, 8));          // 8
        line.append(formatDate(header.getBldat(), dateFormat, 8));          // 8
        line.append(formatDate(header.getValut(), dateFormat, 8));          // 8
        line.append(formatString(header.getXeiph(), 1));                    // 1
        line.append(formatString(header.getAugrd(), 2));                    // 2
        line.append(formatString(header.getXrlsd(), 1));                    // 1
        line.append(formatString(header.getXrlsk(), 1));                    // 1
        line.append(formatString(header.getXsteb(), 1));                    // 1
        line.append(formatString(header.getXrlsb(), 1));                    // 1
        line.append(formatString(header.getBankl(), 3));                    // 3
        line.append(formatString(header.getBankk(), 15));                   // 15
        line.append(formatString(header.getBankn(), 18));                   // 18
        line.append(formatString(header.getHbkid(), 5));                    // 5
        line.append(formatString(header.getHktid(), 5));                    // 5
        line.append(formatString(header.getXcalcgeb(), 1));                 // 1
        line.append(formatString(header.getXacceptcharges(), 1));           // 1
        line.append(formatString(header.getRlmod(), 1));                    // 1
        line.append(formatString(header.getKsums(), 15));                   // 15
        line.append(formatBigDecimal(header.getKsumh(), 15));               // 15
        line.append(formatInteger(header.getKsump(), 6));                   // 6
        line.append(formatString(header.getXerwr(), 1));                    // 1
        line.append(formatString(header.getPrctr(), 10));                   // 10
        
        // Pad to 257 characters if needed
        while (line.length() < 257) {
            line.append(" ");
        }
        
        writer.write(line.toString());
        writer.newLine();
    }

    private void writeDeclinedDetailRecord(BufferedWriter writer, BFKKRP detail) throws IOException {
        StringBuilder line = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        
        // Build fixed-length record (502 characters total)
        line.append(formatString(detail.getStype(), 1));                    // 1
        line.append(formatString(detail.getTbnam(), 30));                   // 30
        line.append(formatString(detail.getSelt1(), 1));                    // 1
        line.append(formatString(detail.getSelw1(), 20));                   // 20
        line.append(formatBigDecimal(detail.getBetrr(), 16));               // 16
        line.append(formatBigDecimal(detail.getBetrh(), 16));               // 16
        line.append(formatBigDecimal(detail.getBtrb1(), 16));               // 16
        line.append(formatBigDecimal(detail.getBtrb2(), 16));               // 16
        line.append(formatBigDecimal(detail.getBtrv1(), 16));               // 16
        line.append(formatBigDecimal(detail.getBtrv2(), 16));               // 16
        line.append(formatBigDecimal(detail.getStbb1(), 16));               // 16
        line.append(formatBigDecimal(detail.getStbb2(), 16));               // 16
        line.append(formatBigDecimal(detail.getStbv1(), 16));               // 16
        line.append(formatBigDecimal(detail.getStbv2(), 16));               // 16
        line.append(formatString(detail.getSkzb1(), 2));                    // 2
        line.append(formatString(detail.getSkzb2(), 2));                    // 2
        line.append(formatString(detail.getSkzv1(), 2));                    // 2
        line.append(formatString(detail.getSkzv2(), 2));                    // 2
        line.append(formatString(detail.getRlsko(), 10));                   // 10
        line.append(formatString(detail.getBukrs(), 4));                    // 4
        line.append(formatString(detail.getGsber(), 4));                    // 4
        line.append(formatString(detail.getBlart(), 2));                    // 2
        line.append(formatString(detail.getWaers(), 5));                    // 5
        line.append(formatBigDecimal(detail.getKursf(), 10));               // 10
        line.append(formatDate(detail.getBudat(), dateFormat, 8));          // 8
        line.append(formatDate(detail.getBldat(), dateFormat, 8));          // 8
        line.append(formatDate(detail.getValut(), dateFormat, 8));          // 8
        line.append(formatString(detail.getXeiph(), 1));                    // 1
        line.append(formatString(detail.getRlgrd(), 3));                    // 3
        line.append(formatString(detail.getRlhbk(), 6));                    // 6
        line.append(formatString(detail.getTxtvw(), 80));                   // 80
        line.append(formatString(detail.getBankl(), 3));                    // 3
        line.append(formatString(detail.getBankk(), 15));                   // 15
        line.append(formatString(detail.getBankn(), 18));                   // 18
        line.append(formatString(detail.getIban(), 34));                    // 34
        line.append(formatString(detail.getChecf(), 16));                   // 16
        line.append(formatString(detail.getXacceptcharges(), 1));           // 1
        line.append(formatString(detail.getHbkid(), 5));                    // 5
        line.append(formatString(detail.getHktid(), 5));                    // 5
        line.append(formatString(detail.getRlmod(), 1));                    // 1
        line.append(formatString(detail.getXerwr(), 1));                    // 1
        line.append(formatString(detail.getPrctr(), 10));                   // 10
        line.append(formatString(detail.getKukey(), 8));                    // 8
        line.append(formatString(detail.getEsnum(), 5));                    // 5
        line.append(formatString(detail.getSwift(), 11));                   // 11
        
        // Pad to 502 characters if needed
        while (line.length() < 502) {
            line.append(" ");
        }
        
        writer.write(line.toString());
        writer.newLine();
    }

    private String formatString(String value, int length) {
        if (value == null) {
            value = "";
        }
        if (value.length() > length) {
            return value.substring(0, length);
        }
        return String.format("%-" + length + "s", value);
    }

    private String formatBigDecimal(BigDecimal value, int length) {
        if (value == null) {
            value = BigDecimal.ZERO;
        }
        String formatted = value.toString();
        return formatString(formatted, length);
    }

    private String formatInteger(Integer value, int length) {
        if (value == null) {
            value = 0;
        }
        return String.format("%0" + length + "d", value);
    }

    private String formatDate(Date date, SimpleDateFormat format, int length) {
        if (date == null) {
            return formatString("", length);
        }
        return formatString(format.format(date), length);
    }
}
