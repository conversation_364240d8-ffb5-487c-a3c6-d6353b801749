package org.example.batchdemo.batch.writer;

import org.example.batchdemo.model.sap.SAPRootGroup;
import org.example.batchdemo.model.sap.declined.SAPDeclinedRootGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Classifier-based ItemWriter that routes SAP records to appropriate writers
 * based on their type (regular SAP or declined SAP)
 */
@Component
public class ClassifiedSapWriter implements ItemWriter<Object> {

    private static final Logger logger = LoggerFactory.getLogger(ClassifiedSapWriter.class);

    @Autowired
    private SapFixedLengthWriter sapFixedLengthWriter;

    @Autowired
    private SapDeclinedFixedLengthWriter sapDeclinedFixedLengthWriter;

    @Override
    public void write(Chunk<? extends Object> chunk) throws Exception {
        logger.debug("Classifying and writing {} items", chunk.size());

        // Separate items by type
        List<SAPRootGroup> sapItems = new ArrayList<>();
        List<SAPDeclinedRootGroup> sapDeclinedItems = new ArrayList<>();

        for (Object item : chunk) {
            if (item instanceof SAPRootGroup) {
                sapItems.add((SAPRootGroup) item);
            } else if (item instanceof SAPDeclinedRootGroup) {
                sapDeclinedItems.add((SAPDeclinedRootGroup) item);
            } else {
                logger.warn("Unknown item type: {}", item.getClass().getName());
            }
        }

        // Write to appropriate writers
        if (!sapItems.isEmpty()) {
            logger.info("Writing {} SAP items", sapItems.size());
            sapFixedLengthWriter.write(new Chunk<>(sapItems));
        }

        if (!sapDeclinedItems.isEmpty()) {
            logger.info("Writing {} SAP declined items", sapDeclinedItems.size());
            sapDeclinedFixedLengthWriter.write(new Chunk<>(sapDeclinedItems));
        }
    }
}
