package org.example.batchdemo.batch.writer;

import org.example.batchdemo.model.sap.BFKKZGR00;
import org.example.batchdemo.model.sap.BFKKZK;
import org.example.batchdemo.model.sap.BFKKZP;
import org.example.batchdemo.model.sap.SAPRootGroup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Native Spring Batch ItemWriter for SAP fixed-length format files
 * Writes files without BeanIO dependency
 */
@Component
public class SapFixedLengthWriter implements ItemWriter<SAPRootGroup> {

    private static final Logger logger = LoggerFactory.getLogger(SapFixedLengthWriter.class);

    @Value("${batch.output.directory:#{systemProperties['java.io.tmpdir']}/batch/output}")
    private String outputDirectory;

    @Value("${sap.client:100}")
    private String sapClient;

    @Override
    public void write(Chunk<? extends SAPRootGroup> chunk) throws Exception {
        logger.info("Writing {} SAP records", chunk.size());

        // Ensure output directory exists
        Path outputPath = Paths.get(outputDirectory);
        if (!Files.exists(outputPath)) {
            Files.createDirectories(outputPath);
            logger.info("Created output directory: {}", outputPath);
        }

        for (SAPRootGroup sapRootGroup : chunk) {
            writeSapFile(sapRootGroup);
        }
    }

    private void writeSapFile(SAPRootGroup sapRootGroup) throws IOException {
        // Generate filename with timestamp
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date());
        String fileName = String.format("sap_payment_%s.txt", timestamp);
        Path outputFile = Paths.get(outputDirectory, fileName);

        logger.info("Writing SAP file: {}", fileName);

        try (BufferedWriter writer = Files.newBufferedWriter(outputFile, 
                StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING)) {
            
            // Write header record (BFKKZGR00)
            if (sapRootGroup.getBfkkzgr00() != null) {
                writeHeaderRecord(writer, sapRootGroup.getBfkkzgr00());
            }

            // Write payment header (BFKKZK)
            if (sapRootGroup.getBfkkzk() != null) {
                writePaymentHeaderRecord(writer, sapRootGroup.getBfkkzk());
            }

            // Write payment details (BFKKZP)
            if (sapRootGroup.getBfkkzpList() != null) {
                for (BFKKZP detail : sapRootGroup.getBfkkzpList()) {
                    writePaymentDetailRecord(writer, detail);
                }
            }
            
            logger.info("Successfully wrote SAP file: {} with {} payment records", 
                fileName, 
                sapRootGroup.getBfkkzpList() != null ? sapRootGroup.getBfkkzpList().size() : 0);
        }
    }

    private void writeHeaderRecord(BufferedWriter writer, BFKKZGR00 header) throws IOException {
        StringBuilder line = new StringBuilder();
        
        // STYPE (1) + MANDT (3) + APPLK (1) = 5 characters
        line.append(formatString(header.getStype(), 1));
        line.append(formatString(header.getMandt() != null ? header.getMandt() : sapClient, 3));
        line.append(formatString(header.getApplk(), 1));
        
        writer.write(line.toString());
        writer.newLine();
    }

    private void writePaymentHeaderRecord(BufferedWriter writer, BFKKZK header) throws IOException {
        StringBuilder line = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        
        // Build fixed-length record (273 characters total)
        line.append(formatString(header.getStype(), 1));                    // 1
        line.append(formatString(header.getTbnam(), 30));                   // 30
        line.append(formatString(header.getKeyz1(), 12));                   // 12
        line.append(formatString(header.getKeyz2(), 40));                   // 40
        line.append(formatString(header.getFikey(), 12));                   // 12
        line.append(formatString(header.getBvrko(), 10));                   // 10
        line.append(formatString(header.getBukrs(), 4));                    // 4
        line.append(formatString(header.getGsber(), 4));                    // 4
        line.append(formatString(header.getBlart(), 2));                    // 2
        line.append(formatString(header.getWaers(), 5));                    // 5
        line.append(formatBigDecimal(header.getKursf(), 10));               // 10
        line.append(formatDate(header.getBudat(), dateFormat, 8));          // 8
        line.append(formatDate(header.getBldat(), dateFormat, 8));          // 8
        line.append(formatDate(header.getValut(), dateFormat, 8));          // 8
        line.append(formatString(header.getXeiph(), 1));                    // 1
        line.append(formatString(header.getAugrd(), 2));                    // 2
        line.append(formatString(header.getXebok(), 1));                    // 1
        line.append(formatString(header.getXposa(), 1));                    // 1
        line.append(formatString(header.getXschs(), 1));                    // 1
        line.append(formatString(header.getInfof(), 50));                   // 50
        line.append(formatString(header.getKtsus(), 15));                   // 15
        line.append(formatString(header.getKtsuh(), 15));                   // 15
        line.append(formatInteger(header.getKsump(), 6));                   // 6
        line.append(formatString(header.getXcrds(), 1));                    // 1
        line.append(formatString(header.getXzaus(), 1));                    // 1
        line.append(formatString(header.getCczah(), 1));                    // 1
        line.append(formatString(header.getXnseb(), 1));                    // 1
        line.append(formatString(header.getCvscd(), 3));                    // 3
        line.append(formatString(header.getPrctr(), 10));                   // 10
        line.append(formatString(header.getKukey(), 8));                    // 8
        line.append(formatString(header.getLtype(), 2));                    // 2
        
        // Pad to 273 characters if needed
        while (line.length() < 273) {
            line.append(" ");
        }
        
        writer.write(line.toString());
        writer.newLine();
    }

    private void writePaymentDetailRecord(BufferedWriter writer, BFKKZP detail) throws IOException {
        StringBuilder line = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        
        // Build fixed-length record (723 characters total)
        line.append(formatString(detail.getStype(), 1));                    // 1
        line.append(formatString(detail.getTbnam(), 30));                   // 30
        line.append(formatString(detail.getSelt1(), 1));                    // 1
        line.append(formatString(detail.getSelt2(), 1));                    // 1
        line.append(formatString(detail.getSelt3(), 1));                    // 1
        line.append(formatString(detail.getSelw1(), 35));                   // 35
        line.append(formatString(detail.getSelw2(), 35));                   // 35
        line.append(formatString(detail.getSelw3(), 35));                   // 35
        line.append(formatBigDecimal(detail.getBetrz(), 16));               // 16
        line.append(formatBigDecimal(detail.getBetrh(), 16));               // 16
        line.append(formatBigDecimal(detail.getTbetr(), 16));               // 16
        line.append(formatString(detail.getBvrko(), 10));                   // 10
        line.append(formatString(detail.getBukrs(), 4));                    // 4
        line.append(formatString(detail.getGsber(), 4));                    // 4
        line.append(formatString(detail.getBlart(), 2));                    // 2
        line.append(formatString(detail.getWaers(), 5));                    // 5
        line.append(formatBigDecimal(detail.getKursf(), 10));               // 10
        line.append(formatDate(detail.getBudat(), dateFormat, 8));          // 8
        line.append(formatDate(detail.getBldat(), dateFormat, 8));          // 8
        line.append(formatDate(detail.getValut(), dateFormat, 8));          // 8
        line.append(formatString(detail.getXeiph(), 1));                    // 1
        line.append(formatString(detail.getAugrd(), 2));                    // 2
        line.append(formatString(detail.getXakon(), 1));                    // 1
        line.append(formatString(detail.getXklae(), 1));                    // 1
        line.append(formatString(detail.getKlaeh(), 10));                   // 10
        line.append(formatString(detail.getTxtvw(), 80));                   // 80
        line.append(formatString(detail.getChckn(), 13));                   // 13
        line.append(formatString(detail.getBanks(), 3));                    // 3
        line.append(formatString(detail.getBankl(), 15));                   // 15
        line.append(formatString(detail.getBankn(), 18));                   // 18
        line.append(formatString(detail.getBkont(), 2));                    // 2
        line.append(formatString(detail.getKoinh(), 60));                   // 60
        line.append(formatString(detail.getXpgro(), 1));                    // 1
        line.append(formatString(detail.getXdaub(), 1));                    // 1
        line.append(formatString(detail.getInfof(), 50));                   // 50
        line.append(formatString(detail.getKukon(), 4));                    // 4
        line.append(formatString(detail.getBkref(), 20));                   // 20
        line.append(formatString(detail.getCcins(), 4));                    // 4
        line.append(formatString(detail.getCcnum(), 25));                   // 25
        line.append(formatDate(detail.getDatab(), dateFormat, 8));          // 8
        line.append(formatDate(detail.getDatbi(), dateFormat, 8));          // 8
        line.append(formatString(detail.getAunum(), 25));                   // 25
        line.append(formatDate(detail.getAudat(), dateFormat, 8));          // 8
        line.append(formatString(detail.getAutim(), 6));                    // 6
        line.append(formatString(detail.getPrctr(), 10));                   // 10
        line.append(formatString(detail.getBegru(), 4));                    // 4
        line.append(formatString(detail.getMerch(), 15));                   // 15
        line.append(formatString(detail.getIban(), 34));                    // 34
        line.append(formatString(detail.getSwift(), 11));                   // 11
        line.append(formatString(detail.getEsnum(), 5));                    // 5
        line.append(formatString(detail.getLnkid(), 32));                   // 32
        
        // Pad to 723 characters if needed
        while (line.length() < 723) {
            line.append(" ");
        }
        
        writer.write(line.toString());
        writer.newLine();
    }

    private String formatString(String value, int length) {
        if (value == null) {
            value = "";
        }
        if (value.length() > length) {
            return value.substring(0, length);
        }
        return String.format("%-" + length + "s", value);
    }

    private String formatBigDecimal(BigDecimal value, int length) {
        if (value == null) {
            value = BigDecimal.ZERO;
        }
        // Convert to string with proper formatting
        String formatted = value.toString();
        return formatString(formatted, length);
    }

    private String formatInteger(Integer value, int length) {
        if (value == null) {
            value = 0;
        }
        return String.format("%0" + length + "d", value);
    }

    private String formatDate(Date date, SimpleDateFormat format, int length) {
        if (date == null) {
            return formatString("", length);
        }
        return formatString(format.format(date), length);
    }
}
