package org.example.batchdemo.batch;

import org.example.batchdemo.model.eigen.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class EigenFileReader implements ItemReader<EigenRootGroup> {

    private static final Logger logger = LoggerFactory.getLogger(EigenFileReader.class);

    @Value("${batch.input.directory:#{systemProperties['java.io.tmpdir']}/batch/input}")
    private String inputDirectory;

    private List<Path> filesToProcess;
    private int currentFileIndex = 0;

    public EigenFileReader() {
        this.filesToProcess = new ArrayList<>();
    }

    @Override
    public EigenRootGroup read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
        if (filesToProcess.isEmpty()) {
            loadFilesToProcess();
        }

        if (currentFileIndex >= filesToProcess.size()) {
            return null; // No more files to process
        }

        Path currentFile = filesToProcess.get(currentFileIndex);
        currentFileIndex++;

        logger.info("Processing file: {}", currentFile.getFileName());

        try {
            EigenRootGroup rootGroup = parseEigenFile(currentFile);
            if (rootGroup != null) {
                logger.info("Successfully read file: {}", currentFile.getFileName());
                return rootGroup;
            } else {
                logger.warn("No data found in file: {}", currentFile.getFileName());
                return read(); // Try next file
            }
        } catch (IOException e) {
            logger.error("Error reading file: {}", currentFile.getFileName(), e);
            throw new ParseException("Failed to read file: " + currentFile.getFileName(), e);
        }
    }

    private EigenRootGroup parseEigenFile(Path filePath) throws IOException {
        EigenRootGroup rootGroup = new EigenRootGroup();
        List<EigenBatch> batches = new ArrayList<>();
        EigenBatch currentBatch = null;
        List<EigenBatchDetail> currentDetails = new ArrayList<>();

        SimpleDateFormat dateFormat = new SimpleDateFormat("MMddyyyy");

        try (BufferedReader reader = Files.newBufferedReader(filePath)) {
            String line;
            int lineNumber = 0;

            while ((line = reader.readLine()) != null) {
                lineNumber++;

                if (line.trim().isEmpty()) {
                    continue;
                }

                // Parse based on record type (first character)
                char recordType = line.charAt(0);

                switch (recordType) {
                    case 'F': // File Header
                        EigenFileHeader fileHeader = parseFileHeader(line);
                        rootGroup.setFileHeader(fileHeader);
                        break;
                    case 'B': // Batch Header
                        // Save previous batch if exists
                        if (currentBatch != null) {
                            currentBatch.setBatchDetails(new ArrayList<>(currentDetails));
                            batches.add(currentBatch);
                            currentDetails.clear();
                        }
                        currentBatch = parseBatchHeader(line);
                        break;
                    case 'D': // Detail Record
                        EigenBatchDetail detail = parseDetailRecord(line, lineNumber);
                        if (detail != null) {
                            currentDetails.add(detail);
                        }
                        break;
                    case 'T': // Batch Trailer
                        EigenBatchTrailer batchTrailer = parseBatchTrailer(line);
                        if (currentBatch != null) {
                            currentBatch.setBatchTrailer(batchTrailer);
                        }
                        break;
                    case 'R': // File Trailer
                        EigenFileTrailer fileTrailer = parseFileTrailer(line);
                        rootGroup.setFileTrailer(fileTrailer);
                        break;
                    default:
                        logger.warn("Unknown record type '{}' at line {}", recordType, lineNumber);
                }
            }

            // Add final batch
            if (currentBatch != null) {
                currentBatch.setBatchDetails(new ArrayList<>(currentDetails));
                batches.add(currentBatch);
            }

            rootGroup.setBatches(batches);
        }

        logger.info("Parsed file {} with {} batches", filePath.getFileName(), batches.size());
        return rootGroup;
    }

    private EigenFileHeader parseFileHeader(String line) {
        EigenFileHeader header = new EigenFileHeader();
        header.setRecordType(parseString(line, 0, 1));
        header.setSubmissionDate(parseDate(line, 1, 9));
        header.setProcessedDate(parseDate(line, 9, 17));
        header.setOfSeqNumber(parseLong(line, 17, 25));
        header.setEfSeqNumber(parseLong(line, 25, 33));
        header.setUid(parseString(line, 33, 53).trim());
        return header;
    }

    private EigenBatch parseBatchHeader(String line) {
        EigenBatch batch = new EigenBatch();
        EigenBatchHeader header = new EigenBatchHeader();
        header.setRecordType(parseString(line, 0, 1));
        header.setBatchId(parseString(line, 1, 9).trim());
        header.setBatchDate(parseDate(line, 9, 17));
        batch.setBatchHeader(header);
        return batch;
    }

    private EigenBatchTrailer parseBatchTrailer(String line) {
        EigenBatchTrailer trailer = new EigenBatchTrailer();
        trailer.setRecordType(parseString(line, 0, 1));
        trailer.setBatchId(parseString(line, 1, 9).trim());
        trailer.setTotalDetailLinesInBatch(parseInteger(line, 9, 15));
        trailer.setTotalAmountInBatch(parseBigDecimal(line, 15, 23));
        return trailer;
    }

    private EigenFileTrailer parseFileTrailer(String line) {
        EigenFileTrailer trailer = new EigenFileTrailer();
        trailer.setRecordType(parseString(line, 0, 1));
        trailer.setFileId(parseString(line, 1, 9).trim());
        trailer.setTotalBatchesInFile(parseInteger(line, 9, 15));
        trailer.setTotalDetailLinesInFile(parseInteger(line, 15, 21));
        trailer.setTotalAmountInFile(parseBigDecimal(line, 21, 29));
        return trailer;
    }

    private EigenBatchDetail parseDetailRecord(String line, int lineNumber) {
        try {
            if (line.length() < 200) { // Minimum expected length
                logger.warn("Detail record too short at line {}: expected at least 200 chars, got {}", lineNumber, line.length());
                return null;
            }

            EigenBatchDetail detail = new EigenBatchDetail();

            // Parse fixed-length fields based on Eigen format specification
            detail.setRecordType(parseString(line, 0, 1));
            detail.setOdSeqNumber(parseString(line, 1, 9).trim());
            detail.setProcessedCode(parseString(line, 9, 13).trim());
            detail.setResponseCode(parseString(line, 13, 17).trim());
            detail.setErrorCode(parseString(line, 17, 21).trim());
            detail.setErrorDesc(parseString(line, 21, 61).trim());
            detail.setAmount(parseBigDecimal(line, 61, 69));
            detail.setTermId(parseString(line, 69, 85).trim());
            detail.setTrack2Acc(parseString(line, 85, 105).trim());
            detail.setApprovalCd(parseString(line, 105, 115).trim());
            detail.setInvoiceNum(parseString(line, 115, 125).trim());
            detail.setExtOpId(parseString(line, 125, 141).trim());
            detail.setClientId(parseString(line, 141, 157).trim());
            detail.setCustId(parseString(line, 157, 169).trim());
            detail.setPaymentType(parseString(line, 169, 170).trim());
            detail.setAmount3(parseBigDecimal(line, 170, 178));
            detail.setTaxAmount(parseBigDecimal(line, 178, 186));
            detail.setPaymentSource(parseString(line, 186, 187).trim());
            detail.setReferenceNumber(parseString(line, 187, 197).trim());

            return detail;

        } catch (Exception e) {
            logger.error("Error parsing detail record at line {}: {}", lineNumber, e.getMessage());
            return null;
        }
    }

    private String parseString(String line, int start, int end) {
        if (start >= line.length()) {
            return "";
        }
        int actualEnd = Math.min(end, line.length());
        return line.substring(start, actualEnd);
    }

    private BigDecimal parseBigDecimal(String line, int start, int end) {
        String value = parseString(line, start, end).trim();
        if (value.isEmpty() || "0".equals(value)) {
            return BigDecimal.ZERO;
        }
        try {
            // Handle amounts that might be in cents (divide by 100)
            BigDecimal amount = new BigDecimal(value);
            return amount.divide(new BigDecimal("100"));
        } catch (NumberFormatException e) {
            logger.warn("Invalid number format: {}", value);
            return BigDecimal.ZERO;
        }
    }

    private Integer parseInteger(String line, int start, int end) {
        String value = parseString(line, start, end).trim();
        if (value.isEmpty()) {
            return 0;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.warn("Invalid integer format: {}", value);
            return 0;
        }
    }

    private Date parseDate(String line, int start, int end) {
        String dateStr = parseString(line, start, end).trim();
        if (dateStr.isEmpty()) {
            return null;
        }
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("MMddyyyy");
            return dateFormat.parse(dateStr);
        } catch (Exception e) {
            logger.warn("Invalid date format: {}", dateStr);
            return null;
        }
    }

    private void loadFilesToProcess() throws IOException {
        Path inputPath = Paths.get(inputDirectory);
        if (!Files.exists(inputPath)) {
            Files.createDirectories(inputPath);
            logger.info("Created input directory: {}", inputPath);
            return;
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(inputPath, "*.txt")) {
            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    filesToProcess.add(file);
                    logger.info("Found file to process: {}", file.getFileName());
                }
            }
        }

        logger.info("Found {} files to process", filesToProcess.size());
    }

    public void reset() {
        currentFileIndex = 0;
        filesToProcess.clear();
    }
}
