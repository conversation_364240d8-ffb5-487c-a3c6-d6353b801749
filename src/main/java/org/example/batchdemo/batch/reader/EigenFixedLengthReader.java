package org.example.batchdemo.batch.reader;

import org.example.batchdemo.model.eigen.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Native Spring Batch ItemReader for Eigen fixed-length format files
 * Reads files without BeanIO dependency
 */
@Component
public class EigenFixedLengthReader implements ItemReader<EigenBatchDetail> {

    private static final Logger logger = LoggerFactory.getLogger(EigenFixedLengthReader.class);

    @Value("${batch.input.directory:#{systemProperties['java.io.tmpdir']}/batch/input}")
    private String inputDirectory;

    private List<Path> filesToProcess;
    private int currentFileIndex = 0;
    private Iterator<EigenBatchDetail> currentDetailIterator;
    private String currentFileName;

    public EigenFixedLengthReader() {
        this.filesToProcess = new ArrayList<>();
    }

    @Override
    public EigenBatchDetail read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
        
        // If we have details from current file, return next one
        if (currentDetailIterator != null && currentDetailIterator.hasNext()) {
            EigenBatchDetail detail = currentDetailIterator.next();
            logger.debug("Read detail record: {}", detail.getOdSeqNumber());
            return detail;
        }

        // Need to load next file
        if (filesToProcess.isEmpty()) {
            loadFilesToProcess();
        }

        if (currentFileIndex >= filesToProcess.size()) {
            logger.info("No more files to process");
            return null; // No more files to process
        }

        // Load next file
        Path currentFile = filesToProcess.get(currentFileIndex);
        currentFileIndex++;
        currentFileName = currentFile.getFileName().toString();

        logger.info("Processing file: {}", currentFileName);

        try {
            List<EigenBatchDetail> allDetails = parseEigenFile(currentFile);
            currentDetailIterator = allDetails.iterator();
            
            if (currentDetailIterator.hasNext()) {
                EigenBatchDetail detail = currentDetailIterator.next();
                logger.info("Started processing file {} with {} total details", currentFileName, allDetails.size());
                return detail;
            } else {
                logger.warn("No details found in file: {}", currentFileName);
                return read(); // Try next file
            }
        } catch (IOException e) {
            logger.error("Error reading file: {}", currentFileName, e);
            throw new ParseException("Failed to read file: " + currentFileName, e);
        }
    }

    private List<EigenBatchDetail> parseEigenFile(Path filePath) throws IOException {
        List<EigenBatchDetail> allDetails = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMddyyyy");
        
        try (BufferedReader reader = Files.newBufferedReader(filePath)) {
            String line;
            int lineNumber = 0;
            
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                
                if (line.trim().isEmpty()) {
                    continue;
                }
                
                // Parse based on record type (first character)
                char recordType = line.charAt(0);
                
                switch (recordType) {
                    case 'F': // File Header
                        logger.debug("Parsed file header at line {}", lineNumber);
                        break;
                    case 'B': // Batch Header
                        logger.debug("Parsed batch header at line {}", lineNumber);
                        break;
                    case 'D': // Detail Record
                        EigenBatchDetail detail = parseDetailRecord(line, lineNumber);
                        if (detail != null) {
                            allDetails.add(detail);
                        }
                        break;
                    case 'T': // Batch Trailer
                        logger.debug("Parsed batch trailer at line {}", lineNumber);
                        break;
                    case 'R': // File Trailer
                        logger.debug("Parsed file trailer at line {}", lineNumber);
                        break;
                    default:
                        logger.warn("Unknown record type '{}' at line {}", recordType, lineNumber);
                }
            }
        }
        
        logger.info("Parsed {} detail records from file {}", allDetails.size(), filePath.getFileName());
        return allDetails;
    }

    private EigenBatchDetail parseDetailRecord(String line, int lineNumber) {
        try {
            if (line.length() < 236) {
                logger.warn("Detail record too short at line {}: expected 236 chars, got {}", lineNumber, line.length());
                return null;
            }

            EigenBatchDetail detail = new EigenBatchDetail();
            
            // Parse fixed-length fields based on Eigen format specification
            detail.setRecordType(parseString(line, 0, 1));
            detail.setOdSeqNumber(parseString(line, 1, 9).trim());
            detail.setProcessedCode(parseString(line, 9, 13).trim());
            detail.setResponseCode(parseString(line, 13, 17).trim());
            detail.setErrorCode(parseString(line, 17, 21).trim());
            detail.setErrorDesc(parseString(line, 21, 61).trim());
            detail.setAmount(parseBigDecimal(line, 61, 69));
            detail.setTermId(parseString(line, 69, 85).trim());
            detail.setTrack2Acc(parseString(line, 85, 105).trim());
            detail.setApprovalCd(parseString(line, 105, 115).trim());
            detail.setInvoiceNum(parseString(line, 115, 125).trim());
            detail.setExtOpId(parseString(line, 125, 141).trim());
            detail.setClientId(parseString(line, 141, 157).trim());
            detail.setCustId(parseString(line, 157, 169).trim());
            detail.setPaymentType(parseString(line, 169, 170).trim());
            detail.setAmount3(parseBigDecimal(line, 170, 178));
            detail.setTaxAmount(parseBigDecimal(line, 178, 186));
            detail.setPaymentSource(parseString(line, 186, 187).trim());
            detail.setReferenceNumber(parseString(line, 187, 197).trim());
            
            return detail;
            
        } catch (Exception e) {
            logger.error("Error parsing detail record at line {}: {}", lineNumber, e.getMessage());
            return null;
        }
    }

    private String parseString(String line, int start, int end) {
        if (start >= line.length()) {
            return "";
        }
        int actualEnd = Math.min(end, line.length());
        return line.substring(start, actualEnd);
    }

    private BigDecimal parseBigDecimal(String line, int start, int end) {
        String value = parseString(line, start, end).trim();
        if (value.isEmpty() || "0".equals(value)) {
            return BigDecimal.ZERO;
        }
        try {
            // Handle amounts that might be in cents (divide by 100)
            BigDecimal amount = new BigDecimal(value);
            return amount.divide(new BigDecimal("100"));
        } catch (NumberFormatException e) {
            logger.warn("Invalid number format: {}", value);
            return BigDecimal.ZERO;
        }
    }

    private Date parseDate(String line, int start, int end) {
        String dateStr = parseString(line, start, end).trim();
        if (dateStr.isEmpty()) {
            return null;
        }
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("MMddyyyy");
            return dateFormat.parse(dateStr);
        } catch (Exception e) {
            logger.warn("Invalid date format: {}", dateStr);
            return null;
        }
    }

    private void loadFilesToProcess() throws IOException {
        Path inputPath = Paths.get(inputDirectory);
        if (!Files.exists(inputPath)) {
            Files.createDirectories(inputPath);
            logger.info("Created input directory: {}", inputPath);
            return;
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(inputPath, "*.txt")) {
            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    filesToProcess.add(file);
                    logger.info("Found file to process: {}", file.getFileName());
                }
            }
        }

        logger.info("Found {} files to process", filesToProcess.size());
    }

    public void reset() {
        currentFileIndex = 0;
        filesToProcess.clear();
        currentDetailIterator = null;
        currentFileName = null;
    }

    public String getCurrentFileName() {
        return currentFileName;
    }
}
