# OPAC File Processing - Spring Batch Application

This application has been converted from Apache Camel to Spring Batch for processing OPAC payment files.

## Overview

The application processes OPAC payment files in Eigen format, validates them, routes payments by type (Amex, Mastercard, Visa, Declined), and converts them to SAP format.

## Architecture

### Package Structure
```
org.example.batchdemo
├── model.eigen          # Data models for Eigen format
├── batch               # Spring Batch components
├── config              # Batch configuration
├── controller          # REST endpoints
├── service             # Business services
├── exception           # Custom exceptions
└── util                # Utility classes
```

### Key Components

#### Data Models (`org.example.batchdemo.model.eigen`)
- `EigenRootGroup` - Root container for file data
- `EigenFileHeader/Trailer` - File-level metadata
- `EigenBatch` - Batch container
- `EigenBatchHeader/Trailer` - Batch-level metadata
- `EigenBatchDetail` - Individual payment records

#### Batch Components (`org.example.batchdemo.batch`)
- `EigenFileReader` - Reads complete Eigen format files (native implementation)
- `EigenFixedLengthReader` - Reads individual Eigen batch details (native implementation)
- `SapFixedLengthWriter` - Writes SAP format files (native implementation)
- `SapDeclinedFixedLengthWriter` - Writes SAP declined format files (native implementation)
- `ValidationProcessor` - Validates business rules and file integrity
- `PaymentRouter` - Routes payments by type (AX, MC, VI, DECLINED)
- `PaymentProcessor` - Main processing orchestrator
- `DuplicateFileChecker` - Prevents duplicate file processing
- `EigenToSapProcessor` - Converts Eigen records to SAP format
- `ClassifiedSapWriter` - Routes SAP records to appropriate writers

#### Configuration (`org.example.batchdemo.config`)
- `BatchConfiguration` - Spring Batch job and step definitions

#### REST API (`org.example.batchdemo.controller`)
- `FileProcessingController` - Provides REST endpoints for triggering processing

## REST Endpoints

### POST `/api/opac/fetch`
Initiates file fetching process.

### POST `/api/opac/router`
Starts the main file processing job.

### POST `/api/opac/validate`
Triggers file validation.

## Configuration

### Application Properties
```properties
# Spring Batch Configuration
spring.batch.jdbc.initialize-schema=always
spring.batch.job.enabled=false

# Database Configuration (H2 for Spring Batch metadata)
spring.datasource.url=jdbc:h2:mem:batchdb

# Batch Processing Directories
batch.input.directory=${java.io.tmpdir}/batch/input
batch.output.directory=${java.io.tmpdir}/batch/output
batch.done.directory=${java.io.tmpdir}/batch/done
batch.error.directory=${java.io.tmpdir}/batch/error

# Duplicate File Check Configuration
batch.duplicate.fileCheck.age=10
```

## Key Features

### 1. File Processing Pipeline
- **Read**: BeanIO-based reading of fixed-length Eigen format files
- **Validate**: Business rules validation including totals and counts
- **Route**: Payment routing by card type and status
- **Process**: Data transformation and mapping

### 2. Business Rules Validation
- File structure validation (header, batches, trailer)
- Batch count and amount validation
- Detail record count validation
- Cross-field validation

### 3. Payment Routing
Payments are routed based on:
- **Amex (AX)**: Payment type 'A'
- **Mastercard (MC)**: Payment type 'M'
- **Visa (VI)**: Payment type 'V'
- **Declined**: Error codes or failed response codes

### 4. Duplicate Detection
- Checks against archived files in done directory
- Configurable age limit for duplicate checking
- Filename and sequence number validation

## Running the Application

### Prerequisites
- Java 17
- Maven 3.6+

### Build and Run
```bash
mvn clean compile
mvn test
mvn spring-boot:run -Dspring-boot.run.arguments=--server.port=8081
```

### Testing Endpoints
```bash
# Test file fetch
curl -X POST http://localhost:8081/api/opac/fetch

# Test file validation
curl -X POST http://localhost:8081/api/opac/validate

# Test file processing
curl -X POST http://localhost:8081/api/opac/router
```

## Migration from Camel

### What Changed
1. **Framework**: Apache Camel → Spring Batch
2. **Processing Model**: Route-based → Job/Step-based
3. **Configuration**: XML routes → Java configuration
4. **Package Structure**: `com.etr407.*` → `org.example.batchdemo.*`
5. **File Processing**: BeanIO dependency → Native Spring Batch readers/writers

### What Remained
1. **Data Models**: Same structure, updated package names
2. **Business Logic**: Same validation and routing rules
3. **REST Endpoints**: Same API contract
4. **File Formats**: Same Eigen and SAP fixed-length formats

### Benefits of Spring Batch
1. **Better Error Handling**: Built-in retry and skip mechanisms
2. **Monitoring**: Job execution tracking and metrics
3. **Scalability**: Chunk-based processing and partitioning
4. **Transaction Management**: Robust transaction handling
5. **Restart Capability**: Job restart from failure points
6. **No External Dependencies**: Removed BeanIO dependency for simpler maintenance

## Database

The application uses H2 in-memory database for Spring Batch metadata tables:
- `BATCH_JOB_INSTANCE`
- `BATCH_JOB_EXECUTION`
- `BATCH_STEP_EXECUTION`
- etc.

Access H2 console at: http://localhost:8081/h2-console
- JDBC URL: `jdbc:h2:mem:batchdb`
- Username: `sa`
- Password: (empty)

## BeanIO Removal

### What Was Removed
1. **BeanIO Dependency**: Removed `org.beanio:beanio:2.1.0` from pom.xml
2. **BeanIO Mapping Files**: Removed all XML mapping files:
   - `beanio-mappings.eigen.xml`
   - `beanio-mappings.sap.xml`
   - `beanio-mappings.sap.declined.xml`
3. **BeanIO-based Readers/Writers**: Replaced with native Spring Batch implementations

### Native Implementation Benefits
1. **Reduced Dependencies**: No external library dependencies for file parsing
2. **Better Performance**: Direct parsing without XML configuration overhead
3. **Easier Maintenance**: Pure Java code instead of XML configuration
4. **Better IDE Support**: Full IntelliSense and refactoring support
5. **Simplified Debugging**: Step-through debugging of parsing logic
6. **Type Safety**: Compile-time checking of field mappings

### File Format Support
The native implementation supports the same fixed-length file formats:

#### Eigen Format (Input)
- **File Header (F)**: File metadata and sequence numbers
- **Batch Header (B)**: Batch metadata and identifiers
- **Detail Records (D)**: Individual payment transactions
- **Batch Trailer (T)**: Batch totals and counts
- **File Trailer (R)**: File totals and summary

#### SAP Format (Output)
- **BFKKZGR00**: SAP file header record
- **BFKKZK**: SAP payment header record
- **BFKKZP**: SAP payment detail records

#### SAP Declined Format (Output)
- **BFKKZGR00**: SAP declined file header record
- **BFKKRK**: SAP declined payment header record
- **BFKKRP**: SAP declined payment detail records

### Implementation Details
- **Fixed-Length Parsing**: Native substring-based field extraction
- **Type Conversion**: Built-in conversion for dates, numbers, and strings
- **Error Handling**: Graceful handling of malformed records
- **Validation**: Field length and format validation
- **Padding**: Automatic padding for output file generation

## Future Enhancements

1. **Scheduling**: Add scheduled file processing
2. **Monitoring**: Add metrics and health checks
3. **Error Handling**: Enhanced error reporting and recovery
4. **Testing**: Add integration tests with sample files
5. **Performance**: Add parallel processing for large files
